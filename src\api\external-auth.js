/**
 * 外部鉴权API
 * 用于在线版与外部鉴权系统的交互
 */
import request from '@/utils/request'
import { getExternalAuthApi, getExternalUserInfoApi } from '@/utils/auth-config'

// 验证外部token
export function validateExternalToken(token) {
  return request({
    url: `${getExternalAuthApi()}/validate`,
    method: 'post',
    data: { token }
  })
}

// 获取外部用户信息
export function getExternalUserInfo() {
  return request({
    url: getExternalUserInfoApi(),
    method: 'get'
  })
}

// 外部登出
export function externalLogout() {
  return request({
    url: `${getExternalAuthApi()}/logout`,
    method: 'post'
  })
}

// 刷新外部token
export function refreshExternalToken(refreshToken) {
  return request({
    url: `${getExternalAuthApi()}/refresh`,
    method: 'post',
    data: { refreshToken }
  })
}
