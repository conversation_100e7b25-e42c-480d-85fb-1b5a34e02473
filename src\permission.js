import router from "./router";
import store from "./store";
import { Message } from "element-ui";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import { getToken } from "@/utils/auth"; // get token from cookie
import getPageTitle from "@/utils/get-page-title";
import { authConfig, isLocalAuthEnabled } from "@/utils/auth-config"; // 鉴权配置

import { asyncRoutes } from "@/router";

NProgress.configure({ showSpinner: false }); // NProgress Configuration

// 根据配置获取白名单
const whiteList = isLocalAuthEnabled()
  ? authConfig.local.whiteList
  : authConfig.online.whiteList;

router.beforeEach(async (to, from, next) => {
  // start progress bar
  NProgress.start();

  // set page title
  document.title = getPageTitle(to.meta.title);

  // 如果是在线版，跳过本地鉴权逻辑
  if (!isLocalAuthEnabled()) {
    // 在线版逻辑：直接放行，由外部系统处理鉴权
    next();
    NProgress.done();
    return;
  }

  // 离线版逻辑：使用本地鉴权
  // determine whether the user has logged in
  const hasToken = getToken();

  if (hasToken) {
    if (to.path === "/login") {
      // if is logged in, redirect to the home page
      next({ path: "/" });
      NProgress.done();
    } else {
      if (store.getters.permission_routes.length === 0) {
        // 判断当前用户是否已拉取过路由表信息
        try {
          let { routes } = await store.dispatch("user/getInfo"); // 此方法后续需要通过接口获取路由表数据 获取路由表数据替换routesMap后往下执行

          let routesMap = JSON.parse(routes.detail);
          if (routesMap.at(-1).path !== "*") {
            routesMap.push({ path: "*", redirect: "/404", hidden: true });
          }
          // console.log(routesMap);
          // console.log(JSON.stringify(routesMap));
          // routesMap = JSON.parse(JSON.stringify(routesMap))

          const accessRoutes = await store.dispatch(
            "permission/generateRoutes",
            { asyncRoutes, routesMap }
          );
          router.addRoutes(accessRoutes);
          next({ ...to, replace: true }); // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
        } catch (error) {
          // remove token and go to login page to re-login
          await store.dispatch("user/resetToken");
          Message.error(error || "Has Error");
          next(`/login?redirect=${to.path}`);
          NProgress.done();
        }
      } else {
        // 检查特定页面是否有projectId参数
        const needProjectIdPages = [
          '/project/:projectId/report',
          '/project/:projectId/rules',
          '/project/:projectId/resource-check',
          '/project/:projectId/data-overview',
          '/project/:projectId/branch'
        ];

        // 检查当前路径是否匹配需要projectId的页面
        const isProjectPage = needProjectIdPages.some(pattern => {
          const regex = new RegExp(pattern.replace(':projectId', '[^/]+'));
          return regex.test(to.path);
        });

        // 检查URL中是否包含字面量":projectId"（这是错误的情况）
        const hasLiteralProjectId = to.path.includes(':projectId');

        if (hasLiteralProjectId) {
          // 如果URL中包含字面量":projectId"，说明路由配置有问题，跳转到首页
          next({ path: "/" });
          NProgress.done();
        } else if (isProjectPage && !to.params.projectId) {
          // 如果是项目相关页面但没有projectId参数，跳转到首页
          // console.log('检测到项目页面但缺少projectId参数，跳转到首页');
          next({ path: "/" });
          NProgress.done();
        } else {
          next(); // 当有用户权限的时候，说明所有可访问路由已生成 如访问没权限的全面会自动进入404页面
        }
      }
    }
  } else {
    /* has no token*/
    if (to.path === "/prodetail/reportdetail") {
      // 美术资源详情页跳转访客详情页面
      const { projectId, branch, reportId, keyword } = to.query;
      next(
        `/reportdetail?projectId=${projectId}&branch=${branch}&reportId=${reportId}&keyword=${keyword}`
      );
    } else if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      {
        next();
      }
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`);
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  // finish progress bar
  NProgress.done();
});
