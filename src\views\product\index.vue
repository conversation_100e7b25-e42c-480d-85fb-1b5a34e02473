<template>
  <div class="home-wrapper">
    <!-- 顶部用户栏，固定在顶部 -->
    <div class="top-user-bar">
      <div class="top-bar-content">
        <div class="logo-section">
          <img src="@/assets/project_images/logo.png" alt="Logo" class="logo-image" />
          <h1>AssetsLint</h1>
        </div>
        <!-- <TopBarUser /> -->
      </div>
    </div>

    <div class="home-container" :style="{ backgroundImage: 'url(' + bgImage + ')' }">
      <div class="content-wrapper">
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-input v-model="searchQuery" placeholder="请输入项目名称" class="search-input">
            <el-button slot="append" icon="el-icon-search" @click="searchProjects"></el-button>
          </el-input>
        </div>

        <!-- 项目标题 -->
        <div class="projects-title">
          <h2>我的项目 <span>{{ filteredProjects.length }}个</span></h2>
          <el-button v-if="isAdmin" type="primary" icon="el-icon-plus" @click="handleAddProject">
            新增项目
          </el-button>
        </div>

        <!-- 卡片内容区域 -->
        <div class="projects-area">
          <el-row :gutter="20" v-if="filteredProjects.length > 0">
            <el-col v-for="project in filteredProjects" :key="project.id" :xs="24" :sm="12" :md="8">
              <el-card class="project-card" shadow="hover" @click.native="navigateToProject(project.id)">
                <div class="card-content">
                  <div class="logo-area">
                    <i v-if="!project.icon" class="el-icon-s-platform" style="font-size: 40px; color: #409EFF;"></i>
                    <el-image
                      v-else
                      :src="project.icon"
                      fit="contain"
                      class="project-icon"
                    ></el-image>
                  </div>
                  <div class="project-info">
                    <h3 class="project-name">{{ project.name }}</h3>
                    <p class="project-description">{{ project.description }}</p>
                  </div>
                </div>
                <!-- hover时显示的操作按钮 -->
                <div v-if="isAdmin" class="card-actions" @click.stop>
                  <div class="action-btn edit-btn" @click="handleEditProject(project)" title="编辑">
                    <i class="el-icon-edit"></i>
                  </div>
                  <div class="action-btn delete-btn" @click="handleDeleteProject(project)" title="删除">
                    <i class="el-icon-delete"></i>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 空状态组件 -->
          <el-empty
            v-else
            description="暂无项目，请联系管理员添加"
            :image-size="200"
          >
            <template slot="image">
              <i class="el-icon-folder-add empty-icon" style="font-size: 80px;"></i>
            </template>
          </el-empty>
        </div>
      </div>
    </div>

    <!-- 新增/编辑项目弹窗 -->
    <project-dialog
      :visible.sync="dialogVisible"
      :dialog-type="dialogType"
      :project-data="currentProject"
      @confirm="handleConfirmProject"
    />
  </div>
</template>

<script>
// import TopBarUser from './components/TopBarUser.vue';
import {getProjectList, deleteProject} from '@/api/permission-project'
import { mapActions, mapState } from 'vuex'
import ProjectDialog from './components/ProjectDialog.vue'
export default {
  name: 'ProductIndex',
  components: {
    // TopBarUser
    ProjectDialog
  },
  data() {
    return {
      searchQuery: '',
      bgImage: require('@/assets/bg.png'),
      // 项目列表数据
      projectList: [],
      baseUrl: process.env.VUE_APP_BASE_API, // 图片基础路径
      // 弹窗相关
      dialogVisible: false,
      dialogType: 'new', // 'new' | 'edit'
      currentProject: {}
    };
  },
  computed: {
    ...mapState('user', ['isAdmin']),
    // 过滤项目列表
    filteredProjects() {
      if (!this.searchQuery) return this.projectList;
      return this.projectList.filter(project =>
        project.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        project.description.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    }
  },
  mounted() {
    // 组件挂载后的逻辑
    this.fetchProjectList();
  },
  activated() {
    // 组件激活时的逻辑（针对keep-alive情况）
    this.fetchProjectList();
  },
  methods: {
    ...mapActions('project', [
      'setProjectId',
      'setProjectName'
    ]),

    // 获取项目列表数据
    async fetchProjectList() {
      try {
        const res = await getProjectList({ pageNum: 1, pageSize: 1000 });
        if (res.code === 200 && res.projectList) {
          this.projectList = res.projectList.map((item) => ({
            id: item.projectId,
            name: item.projectName,
            description: item.description || '暂无描述',
            icon:`${this.baseUrl}/media/sculpture/${item.projectId}.png?time=${Date.now()}`||'', // API 返回的数据中没有 icon 字段，保持为空
            createTime: item.createTime || ''
          }));
        } else {
          this.projectList = [];
          this.$message.error(res.msg || '获取项目列表失败');
        }
      } catch (error) {
        console.error('获取项目列表失败:', error);
        this.projectList = [];
        this.$message.error('获取项目列表失败');
      }
    },

    // 搜索项目
    searchProjects() {
      // 搜索逻辑已通过计算属性实现
    },

    // 项目卡片点击导航
    navigateToProject(id) {
      // 查找项目信息
      const project = this.projectList.find(p => p.id === id);

      if (project) {
        // 记录项目信息到 Vuex
        this.setProjectId(id);
        this.setProjectName(project.name);

        // console.log('记录项目信息到 Vuex:', { id, name: project.name });
      }

      // 保存到 sessionStorage（保持兼容性）
      sessionStorage.setItem('currentProjectId', id);

      // 导航到项目页面
      this.$router.push(`/project/${id}/report`);
      // console.log('导航到项目:', id);
    },

    // 新增项目
    handleAddProject() {
      this.dialogType = 'new';
      this.currentProject = {};
      this.dialogVisible = true;
    },

    // 编辑项目
    handleEditProject(project) {
      this.dialogType = 'edit';
      this.currentProject = {
        projectId: project.id,
        projectName: project.name,
        description: project.description
      };
      this.dialogVisible = true;
    },

    // 删除项目
    handleDeleteProject(project) {
      this.$confirm(`确定要删除项目"${project.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await deleteProject({ projectId: project.id });
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.fetchProjectList(); // 重新获取项目列表
          } else {
            this.$message.error(res.msg || '删除失败');
          }
        } catch (error) {
          console.error('删除项目失败:', error);
          this.$message.error('删除失败');
        }
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 确认项目操作（新增/编辑）
    handleConfirmProject() {
      this.dialogVisible = false;
      this.fetchProjectList(); // 重新获取项目列表
    }
  }
};
</script>
<style scoped lang="scss">
.home-wrapper {
  width: 100%;
  min-height: 100vh;
  position: relative;
}

.home-container {
  min-height: calc(100vh - 0px); /* 减去顶部导航栏的高度 */
  height: auto; /* 允许容器高度自适应内容 */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed; /* 背景固定，滚动时不动 */
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: auto; /* 添加垂直滚动条 */
  padding-top: 80px; /* 为固定的顶部栏留出空间 */
  width: 100%; /* 确保宽度为100% */
}

.content-wrapper {
  width: 1200px;
  max-width: calc(100% - 40px); /* 考虑边距 */
  padding: 20px;
  margin-top: 0; /* 移除顶部边距，因为已经在home-container中添加了padding-top */
  padding-bottom: 50px; /* 在底部添加一些空间 */
  position: relative; /* 确保正确定位 */
}

.top-user-bar {
  position: fixed; /* 固定定位 */
  top: 0; /* 置于顶部 */
  left: 0;
  width: 100%;
  background-color: #ffffff; /* 使用不透明背景 */
  height: 60px;
  z-index: 99; /* 确保在最上层 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */

  .top-bar-content {
    width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 20px;
    background-color: #ffffff; /* 使用不透明背景 */
  }

  .logo-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .logo-image {
      height: 32px;
      width: auto;
      object-fit: contain;
    }

    h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
      letter-spacing: 0.5px;
    }
  }
}

.search-area {
  max-width: 500px;
  margin: 20px auto 40px; /* 增加顶部边距 */
  padding: 0;

  .search-input {
    width: 100%;
  }
}

.projects-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
  flex-wrap: wrap; /* 在小屏幕上允许换行 */

  h2 {
    margin: 0 0 10px 0; /* 添加一些底部边距 */
    color: #333;
    font-weight: 500;

    span {
      color: #909399;
      font-size: 16px;
      margin-left: 8px;
      font-weight: normal;
    }
  }
}

.projects-area {
  margin-bottom: 40px;
  max-height: none; /* 移除最大高度限制 */
  width: 100%; /* 确保宽度占满 */

  .el-row {
    width: 100%;
  }

  .empty-icon {
    color: #409EFF;
    margin-bottom: 16px;
  }

  .el-empty {
    padding: 80px 0;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
  }
}

.project-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: rgba(255, 255, 255, 0.9);
  height: 135px;
  position: relative;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .card-actions {
      opacity: 1;
    }
  }

  .card-content {
    display: flex;
    gap: 16px;
    padding: 16px;
    height: 100%;

    .logo-area {
      flex: 0 0 auto;
      width: 60px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;

      .project-icon {
        max-width: 50px;
        max-height: 50px;
        width: auto;
        height: auto;
        object-fit: contain;
      }
    }

    .project-info {
      flex: 1;
      min-width: 0; /* 允许flex子项收缩 */

      .project-name {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%; /* 确保占满父容器宽度 */
      }

      .project-description {
        color: #606266;
        margin: 0 0 12px 0;
        font-size: 12px;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
        max-height: 33.6px; /* 12px * 1.4 * 2 = 33.6px */
      }
    }
  }

  .card-actions {
    position: absolute;
    top: 6px;
    right: 6px;
    display: flex;
    gap: 2px;
    opacity: 0;
    transition: opacity 0.3s ease;

    .action-btn {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      backdrop-filter: blur(4px);

      i {
        font-size: 12px;
      }

      &.edit-btn {
        background-color: rgba(64, 158, 255, 0.8);
        color: white;

        &:hover {
          background-color: rgba(64, 158, 255, 0.95);
          transform: scale(1.05);
        }
      }

      &.delete-btn {
        background-color: rgba(245, 108, 108, 0.8);
        color: white;

        &:hover {
          background-color: rgba(245, 108, 108, 0.95);
          transform: scale(1.05);
        }
      }
    }
  }
}

.el-row {
  margin: 0 !important; /* 覆盖Element UI的默认边距 */
}
</style>