# 项目鉴权模式配置说明

本项目支持两种鉴权模式：**离线版**（本地鉴权）和**在线版**（外部鉴权）。

## 配置方式

### 方式一：环境变量配置（推荐）

通过修改环境配置文件来切换鉴权模式：

#### 离线版配置
```bash
# .env.development 或 .env.production
VUE_APP_ENABLE_LOCAL_AUTH = true
```

#### 在线版配置
```bash
# .env.online
VUE_APP_ENABLE_LOCAL_AUTH = false
VUE_APP_EXTERNAL_LOGIN_URL = 'https://your-sso-login-url.com/login'
VUE_APP_EXTERNAL_AUTH_API = 'https://your-auth-api.com'
VUE_APP_EXTERNAL_USER_INFO_API = 'https://your-auth-api.com/userinfo'
```

## 构建命令

### 离线版构建
```bash
npm run build:offline
```

### 在线版构建
```bash
npm run build:online
```

## 功能差异

### 离线版（本地鉴权）
- ✅ 使用项目内置的登录页面
- ✅ 本地用户管理和权限控制
- ✅ 完整的路由权限验证
- ✅ 支持用户注册、密码修改等功能

### 在线版（外部鉴权）
- ✅ 跳转到外部SSO登录系统
- ✅ 接收外部系统的用户信息
- ✅ 跳过本地权限验证逻辑
- ❌ 不显示本地登录页面
- ❌ 不使用本地用户管理功能

## 在线版集成步骤

1. **配置外部登录地址**
   ```bash
   VUE_APP_EXTERNAL_LOGIN_URL = 'https://your-company.com/sso/login'
   ```

2. **配置外部API接口**
   ```bash
   VUE_APP_EXTERNAL_AUTH_API = 'https://your-company.com/api/auth'
   VUE_APP_EXTERNAL_USER_INFO_API = 'https://your-company.com/api/userinfo'
   ```

3. **实现外部API接口**（后端需要提供）
   - `GET /api/userinfo` - 获取当前用户信息
   - `POST /api/auth/validate` - 验证token有效性
   - `POST /api/auth/logout` - 登出接口

4. **修改外部API实现**
   编辑 `src/api/external-auth.js` 文件，根据实际的外部API接口调整请求格式。

## 注意事项

1. **在线版需要外部系统支持**：确保外部SSO系统能够正确处理登录和用户信息获取。

2. **跨域问题**：在线版可能需要处理跨域请求，确保外部API支持CORS或配置代理。

3. **Token传递**：外部系统登录成功后，需要将token传递给本项目（通过URL参数、postMessage等方式）。

4. **路由权限**：在线版跳过了本地路由权限验证，如需权限控制，需要在外部系统中实现。

## 开发调试

### 本地开发离线版
```bash
npm run dev
```

### 本地开发在线版
1. 修改 `.env.development` 中的配置：
   ```bash
   VUE_APP_ENABLE_LOCAL_AUTH = false
   ```
2. 启动开发服务器：
   ```bash
   npm run dev
   ```

## 部署建议

建议为离线版和在线版分别创建不同的部署包：

```bash
# 构建离线版
npm run build:offline

# 构建在线版  
npm run build:online
```

这样可以确保两个版本的配置完全独立，避免混淆。
