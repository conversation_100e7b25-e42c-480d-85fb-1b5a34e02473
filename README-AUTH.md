# 项目鉴权模式配置说明

本项目支持两种鉴权模式：**离线版**（本地鉴权）和**在线版**（无鉴权）。

## 配置方式

### 环境变量配置

通过修改环境配置文件来切换鉴权模式：

#### 离线版配置
```bash
# .env.development 或 .env.production
VUE_APP_ENABLE_LOCAL_AUTH = true
```

#### 在线版配置
```bash
# .env.online
VUE_APP_ENABLE_LOCAL_AUTH = false
```

## 构建命令

### 离线版构建
```bash
npm run build:offline
```

### 在线版构建
```bash
npm run build:online
```

## 功能差异

### 离线版（本地鉴权）
- ✅ 使用项目内置的登录页面
- ✅ 本地用户管理和权限控制
- ✅ 完整的路由权限验证
- ✅ 支持用户注册、密码修改等功能

### 在线版（无鉴权）
- ✅ 跳过所有本地鉴权逻辑
- ✅ 直接进入应用主页面
- ✅ 无需登录即可使用所有功能
- ❌ 不显示本地登录页面
- ❌ 不使用本地用户管理功能

## 在线版特点

在线版完全跳过了本地鉴权系统，适用于以下场景：
- 部署在内网环境，无需鉴权
- 已有外部鉴权系统处理用户验证
- 作为嵌入式组件使用，由父系统处理鉴权

## 注意事项

1. **在线版无鉴权**：在线版完全跳过鉴权逻辑，请确保部署环境的安全性。

2. **功能访问**：在线版用户可以访问所有功能，如需权限控制，请在外部系统中实现。

3. **数据安全**：由于无鉴权机制，请确保敏感数据的访问控制在后端API层面实现。

## 开发调试

### 本地开发离线版
```bash
npm run dev
```

### 本地开发在线版
1. 修改 `.env.development` 中的配置：
   ```bash
   VUE_APP_ENABLE_LOCAL_AUTH = false
   ```
2. 启动开发服务器：
   ```bash
   npm run dev
   ```
   访问任何页面都会直接进入，无需登录。

## 部署建议

建议为离线版和在线版分别创建不同的部署包：

```bash
# 构建离线版
npm run build:offline

# 构建在线版  
npm run build:online
```

这样可以确保两个版本的配置完全独立，避免混淆。
