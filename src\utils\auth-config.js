/**
 * 鉴权配置工具类
 * 用于区分离线版和在线版的鉴权方式
 */

// 是否启用本地鉴权（离线版）
export const isLocalAuthEnabled = () => {
  return process.env.VUE_APP_ENABLE_LOCAL_AUTH === 'true'
}

// 是否为在线版
export const isOnlineMode = () => {
  return !isLocalAuthEnabled()
}

// 获取外部登录地址
export const getExternalLoginUrl = () => {
  return process.env.VUE_APP_EXTERNAL_LOGIN_URL || ''
}

// 获取外部鉴权API地址
export const getExternalAuthApi = () => {
  return process.env.VUE_APP_EXTERNAL_AUTH_API || ''
}

// 获取外部用户信息API地址
export const getExternalUserInfoApi = () => {
  return process.env.VUE_APP_EXTERNAL_USER_INFO_API || ''
}

// 鉴权配置对象
export const authConfig = {
  // 本地鉴权配置
  local: {
    enabled: isLocalAuthEnabled(),
    loginPath: '/login',
    tokenKey: 'AssetsLint-Token',
    whiteList: ['/login', '/auth-redirect', '/reportdetail']
  },
  
  // 在线版鉴权配置
  external: {
    enabled: isOnlineMode(),
    loginUrl: getExternalLoginUrl(),
    authApi: getExternalAuthApi(),
    userInfoApi: getExternalUserInfoApi(),
    tokenKey: 'External-Auth-Token',
    whiteList: ['/auth-redirect', '/reportdetail']
  }
}

export default authConfig
