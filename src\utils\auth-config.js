/**
 * 鉴权配置工具类
 * 用于区分离线版和在线版的鉴权方式
 */

// 是否启用本地鉴权（离线版）
export const isLocalAuthEnabled = () => {
  return process.env.VUE_APP_ENABLE_LOCAL_AUTH === 'true'
}

// 是否为在线版
export const isOnlineMode = () => {
  return !isLocalAuthEnabled()
}

// 鉴权配置对象
export const authConfig = {
  // 本地鉴权配置
  local: {
    enabled: isLocalAuthEnabled(),
    loginPath: '/login',
    tokenKey: 'AssetsLint-Token',
    whiteList: ['/login', '/auth-redirect', '/reportdetail']
  },

  // 在线版配置（无需鉴权，直接放行）
  online: {
    enabled: isOnlineMode(),
    whiteList: ['/auth-redirect', '/reportdetail']
  }
}

export default authConfig
